# 🧪 Webhook Testing Guide

This guide shows you multiple ways to test your webhook data manager in a local environment.

## 🚀 Quick Start

1. **Make sure your server is running:**
   ```bash
   node server.js
   ```

2. **Open the web interface:**
   - Visit: http://localhost:3001

## 🛠️ Testing Methods

### 1. **Automated Test Suite** ⚡
Run the comprehensive test suite:
```bash
node test-webhook.js
```

### 2. **Interactive Testing Tool** 🎮
Launch the interactive menu-driven tester:
```bash
node interactive-test.js
```

### 3. **Manual cURL Testing** 💻

#### Receive Webhooks:
```bash
# Single record
curl -X POST http://localhost:3001/webhook/receive \
  -H "Content-Type: application/json" \
  -d '{
    "listName": "Customer Data",
    "data": {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "signup_date": "2023-12-01T10:00:00Z"
    }
  }'

# Multiple records
curl -X POST http://localhost:3001/webhook/receive \
  -H "Content-Type: application/json" \
  -d '{
    "listName": "Sales Data",
    "data": [
      {"order": "A001", "amount": 150.00},
      {"order": "A002", "amount": 75.50}
    ]
  }'
```

#### View Data:
```bash
# Get all lists
curl http://localhost:3001/api/lists

# Get records from list ID 1
curl http://localhost:3001/api/lists/1/records
```

#### Send Webhooks:
```bash
curl -X POST http://localhost:3001/api/lists/1/send-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "endpointUrl": "https://httpbin.org/post",
    "customData": {"test": true}
  }'
```

### 4. **Local Webhook Receiver** 🎯

Start a test webhook receiver to catch outgoing webhooks:

```bash
# In a new terminal
node test-webhook-receiver.js
```

This starts a server on port 3002 that will log all received webhooks.

Then send webhooks to it:
```bash
curl -X POST http://localhost:3001/api/lists/1/send-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "endpointUrl": "http://localhost:3002/test-webhook",
    "customData": {"source": "local_test"}
  }'
```

View received webhooks:
```bash
curl http://localhost:3002/received-webhooks
```

### 5. **External Testing Services** 🌐

#### Option A: httpbin.org
- Use `https://httpbin.org/post` as your webhook endpoint
- It will echo back what you sent

#### Option B: webhook.site
1. Go to https://webhook.site
2. Copy your unique URL
3. Use it as your webhook endpoint
4. View real-time webhook data on the website

#### Option C: ngrok (for external testing)
1. Install ngrok: https://ngrok.com/
2. Expose your local server:
   ```bash
   ngrok http 3001
   ```
3. Use the ngrok URL for external webhook testing

## 📊 Testing Scenarios

### Scenario 1: E-commerce Orders
```json
{
  "listName": "E-commerce Orders",
  "data": {
    "order_id": "ORD-12345",
    "customer_email": "<EMAIL>",
    "total_amount": 299.99,
    "items": [
      {"product": "Laptop", "quantity": 1, "price": 299.99}
    ],
    "order_date": "2023-12-01T14:30:00Z",
    "status": "completed"
  }
}
```

### Scenario 2: Form Submissions
```json
{
  "listName": "Contact Forms",
  "data": {
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "message": "Interested in your services",
    "source": "website_contact_form",
    "submitted_at": "2023-12-01T15:45:00Z"
  }
}
```

### Scenario 3: Bulk Data Import
```json
{
  "listName": "User Registrations",
  "data": [
    {
      "user_id": "user_001",
      "username": "alice_j",
      "email": "<EMAIL>",
      "registration_date": "2023-12-01T10:00:00Z"
    },
    {
      "user_id": "user_002", 
      "username": "bob_s",
      "email": "<EMAIL>",
      "registration_date": "2023-12-01T10:05:00Z"
    }
  ]
}
```

## 🔍 Debugging Tips

### Check Server Logs
The server logs will show:
- Incoming webhook requests
- Database operations
- Outgoing webhook attempts
- Any errors

### Verify Database
Check if data is being stored:
```bash
curl http://localhost:3001/api/lists
```

### Test Connectivity
Verify the server is responding:
```bash
curl http://localhost:3001/api/lists
```

### Common Issues

1. **Server not responding:**
   - Check if `node server.js` is running
   - Verify port 3001 is not in use by another application

2. **Webhook not received:**
   - Check JSON syntax
   - Verify Content-Type header is set to `application/json`
   - Check server logs for error messages

3. **Database errors:**
   - Check file permissions in the project directory
   - Verify SQLite3 is properly installed

## 🎯 Testing Checklist

- [ ] ✅ Server starts without errors
- [ ] ✅ Webhook endpoint receives single records
- [ ] ✅ Webhook endpoint receives multiple records
- [ ] ✅ Data is stored in database
- [ ] ✅ API endpoints return data
- [ ] ✅ Web interface loads
- [ ] ✅ Custom fields can be added
- [ ] ✅ Outgoing webhooks can be sent
- [ ] ✅ Webhook logs are recorded

## 🚀 Next Steps

Once testing is complete:

1. **Deploy to production** (consider services like Railway, Render, or Heroku)
2. **Set up external webhook sources** (Google Sheets, Zapier, etc.)
3. **Configure webhook destinations** (Slack, Discord, other APIs)
4. **Add authentication** if needed for production use
5. **Set up monitoring** and logging for production

## 📞 Need Help?

If you encounter issues:
1. Check the server logs for error messages
2. Verify all dependencies are installed (`npm install`)
3. Make sure ports 3001 (and 3002 for test receiver) are available
4. Test with the simplest possible webhook first
