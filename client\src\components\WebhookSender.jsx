import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Send, Clock, CheckCircle, XCircle, AlertCircle, Copy } from 'lucide-react';

function WebhookSender({ listId }) {
  const [list, setList] = useState(null);
  const [endpointUrl, setEndpointUrl] = useState('');
  const [customData, setCustomData] = useState('{}');
  const [sending, setSending] = useState(false);
  const [lastResponse, setLastResponse] = useState(null);
  const [webhookLogs, setWebhookLogs] = useState([]);
  const [showLogs, setShowLogs] = useState(false);

  useEffect(() => {
    fetchListData();
    fetchWebhookLogs();
  }, [listId]);

  const fetchListData = async () => {
    try {
      const response = await axios.get(`/api/lists/${listId}`);
      setList(response.data);
    } catch (error) {
      console.error('Error fetching list:', error);
    }
  };

  const fetchWebhookLogs = async () => {
    try {
      const response = await axios.get(`/api/lists/${listId}/webhook-logs`);
      setWebhookLogs(response.data);
    } catch (error) {
      console.error('Error fetching webhook logs:', error);
    }
  };

  const handleSendWebhook = async (e) => {
    e.preventDefault();
    setSending(true);
    
    try {
      let parsedCustomData = {};
      if (customData.trim()) {
        try {
          parsedCustomData = JSON.parse(customData);
        } catch (parseError) {
          throw new Error('Invalid JSON in custom data field');
        }
      }

      const response = await axios.post(`/api/lists/${listId}/send-webhook`, {
        endpointUrl,
        customData: parsedCustomData
      });

      setLastResponse(response.data);
      fetchWebhookLogs(); // Refresh logs
    } catch (error) {
      setLastResponse({
        success: false,
        error: error.response?.data?.error || error.message
      });
    } finally {
      setSending(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  const getStatusIcon = (status) => {
    if (status >= 200 && status < 300) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (status >= 400) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    } else {
      return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  if (!list) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Send Webhook Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Send Webhook</h3>
        <p className="text-sm text-gray-600 mb-6">
          Send the data from "{list.name}" to an external webhook endpoint.
        </p>

        <form onSubmit={handleSendWebhook} className="space-y-4">
          <div>
            <label htmlFor="endpoint" className="block text-sm font-medium text-gray-700 mb-2">
              Webhook Endpoint URL
            </label>
            <input
              type="url"
              id="endpoint"
              required
              value={endpointUrl}
              onChange={(e) => setEndpointUrl(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              placeholder="https://example.com/webhook"
            />
          </div>

          <div>
            <label htmlFor="customData" className="block text-sm font-medium text-gray-700 mb-2">
              Custom Data (JSON)
            </label>
            <textarea
              id="customData"
              rows={6}
              value={customData}
              onChange={(e) => setCustomData(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm font-mono bg-gray-50 resize-vertical min-h-[120px]"
              placeholder='{"message": "Custom webhook data", "timestamp": "2023-12-01T10:00:00Z"}'
              style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
            />
            <p className="mt-2 text-sm text-gray-500">
              Optional: Add custom fields to include in the webhook payload. Must be valid JSON format.
            </p>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={sending}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {sending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Webhook
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Last Response */}
      {lastResponse && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Last Response</h3>
          <div className={`p-4 rounded-md ${lastResponse.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center">
              {lastResponse.success ? (
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500 mr-2" />
              )}
              <span className={`font-medium ${lastResponse.success ? 'text-green-800' : 'text-red-800'}`}>
                {lastResponse.success ? 'Success' : 'Failed'}
              </span>
              {lastResponse.status && (
                <span className="ml-2 text-sm text-gray-600">
                  Status: {lastResponse.status}
                </span>
              )}
            </div>
            
            {lastResponse.recordsSent && (
              <p className="mt-2 text-sm text-gray-600">
                Sent {lastResponse.recordsSent} records
              </p>
            )}
            
            {lastResponse.error && (
              <p className="mt-2 text-sm text-red-700">
                Error: {lastResponse.error}
              </p>
            )}
            
            {lastResponse.response && (
              <div className="mt-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Response:</span>
                  <button
                    onClick={() => copyToClipboard(JSON.stringify(lastResponse.response, null, 2))}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
                <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                  {JSON.stringify(lastResponse.response, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Webhook Logs */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Webhook History</h3>
            <button
              onClick={() => setShowLogs(!showLogs)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              {showLogs ? 'Hide' : 'Show'} History
            </button>
          </div>
        </div>

        {showLogs && (
          <div className="divide-y divide-gray-200">
            {webhookLogs.length > 0 ? (
              webhookLogs.map((log) => (
                <div key={log.id} className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(log.response_status)}
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {log.endpoint_url}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {new Date(log.sent_at).toLocaleString()}
                          </span>
                          <span>Status: {log.response_status}</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => copyToClipboard(JSON.stringify(log.payload, null, 2))}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>

                  {log.response_data && Object.keys(log.response_data).length > 0 && (
                    <div className="mt-3">
                      <span className="text-sm font-medium text-gray-700">Response:</span>
                      <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                        {JSON.stringify(log.response_data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="p-6 text-center text-gray-500">
                No webhook history yet
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default WebhookSender;
