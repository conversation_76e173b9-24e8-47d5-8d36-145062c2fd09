const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabaseManager {
  constructor() {
    this.db = new sqlite3.Database(path.join(__dirname, 'webhook_data.db'));
    this.initializeTables();
  }

  initializeTables() {
    // Table for storing data lists/imports
    this.db.run(`
      CREATE TABLE IF NOT EXISTS data_lists (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table for storing individual data records
    this.db.run(`
      CREATE TABLE IF NOT EXISTS data_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        list_id INTEGER,
        data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (list_id) REFERENCES data_lists (id) ON DELETE CASCADE
      )
    `);

    // Table for webhook sending logs
    this.db.run(`
      CREATE TABLE IF NOT EXISTS webhook_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        list_id INTEGER,
        endpoint_url TEXT NOT NULL,
        payload TEXT NOT NULL,
        response_status INTEGER,
        response_data TEXT,
        sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (list_id) REFERENCES data_lists (id) ON DELETE CASCADE
      )
    `);

    // Table for custom columns/fields
    this.db.run(`
      CREATE TABLE IF NOT EXISTS custom_fields (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        list_id INTEGER,
        field_name TEXT NOT NULL,
        field_type TEXT DEFAULT 'text',
        field_value TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (list_id) REFERENCES data_lists (id) ON DELETE CASCADE
      )
    `);
  }

  // Data Lists operations
  createDataList(name, description = '') {
    return new Promise((resolve, reject) => {
      this.db.run('INSERT INTO data_lists (name, description) VALUES (?, ?)', [name, description], function(err) {
        if (err) reject(err);
        else resolve(this.lastID);
      });
    });
  }

  getDataLists() {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM data_lists ORDER BY created_at DESC', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  getDataList(id) {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT * FROM data_lists WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  updateDataList(id, name, description) {
    return new Promise((resolve, reject) => {
      this.db.run('UPDATE data_lists SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [name, description, id], function(err) {
        if (err) reject(err);
        else resolve(this.changes);
      });
    });
  }

  deleteDataList(id) {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM data_lists WHERE id = ?', [id], function(err) {
        if (err) reject(err);
        else resolve(this.changes);
      });
    });
  }

  // Data Records operations
  addDataRecord(listId, data) {
    return new Promise((resolve, reject) => {
      this.db.run('INSERT INTO data_records (list_id, data) VALUES (?, ?)', [listId, JSON.stringify(data)], function(err) {
        if (err) reject(err);
        else resolve(this.lastID);
      });
    });
  }

  getDataRecords(listId, limit = 100, offset = 0) {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM data_records WHERE list_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?', [listId, limit, offset], (err, rows) => {
        if (err) reject(err);
        else {
          const records = rows.map(record => ({
            ...record,
            data: JSON.parse(record.data)
          }));
          resolve(records);
        }
      });
    });
  }

  getDataRecordCount(listId) {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT COUNT(*) as count FROM data_records WHERE list_id = ?', [listId], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
  }

  // Webhook Logs operations
  logWebhookSend(listId, endpointUrl, payload, responseStatus, responseData) {
    const stmt = this.db.prepare(`
      INSERT INTO webhook_logs (list_id, endpoint_url, payload, response_status, response_data) 
      VALUES (?, ?, ?, ?, ?)
    `);
    return stmt.run(listId, endpointUrl, JSON.stringify(payload), responseStatus, JSON.stringify(responseData));
  }

  getWebhookLogs(listId, limit = 50) {
    const stmt = this.db.prepare('SELECT * FROM webhook_logs WHERE list_id = ? ORDER BY sent_at DESC LIMIT ?');
    const logs = stmt.all(listId, limit);
    return logs.map(log => ({
      ...log,
      payload: JSON.parse(log.payload),
      response_data: JSON.parse(log.response_data || '{}')
    }));
  }

  // Custom Fields operations
  addCustomField(listId, fieldName, fieldType = 'text', fieldValue = '') {
    const stmt = this.db.prepare('INSERT INTO custom_fields (list_id, field_name, field_type, field_value) VALUES (?, ?, ?, ?)');
    return stmt.run(listId, fieldName, fieldType, fieldValue);
  }

  getCustomFields(listId) {
    const stmt = this.db.prepare('SELECT * FROM custom_fields WHERE list_id = ? ORDER BY created_at');
    return stmt.all(listId);
  }

  updateCustomField(id, fieldValue) {
    const stmt = this.db.prepare('UPDATE custom_fields SET field_value = ? WHERE id = ?');
    return stmt.run(fieldValue, id);
  }

  close() {
    this.db.close();
  }
}

module.exports = DatabaseManager;
