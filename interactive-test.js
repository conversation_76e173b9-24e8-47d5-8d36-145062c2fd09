const axios = require('axios');
const readline = require('readline');

const BASE_URL = 'http://localhost:3001';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function showMenu() {
  console.clear();
  console.log('🧪 Webhook Data Manager - Interactive Testing Tool');
  console.log('==================================================');
  console.log('');
  console.log('Choose an option:');
  console.log('1. 📥 Send test webhook data');
  console.log('2. 📋 View all data lists');
  console.log('3. 👀 View records from a list');
  console.log('4. 🚀 Send webhook to external endpoint');
  console.log('5. 🏷️  Add custom field to a list');
  console.log('6. 📊 View webhook send logs');
  console.log('7. 🌐 Open web interface');
  console.log('8. 📖 Show webhook examples');
  console.log('9. 🔄 Run full test suite');
  console.log('0. ❌ Exit');
  console.log('');
}

async function sendTestWebhook() {
  console.log('\n📥 Sending Test Webhook Data');
  console.log('============================');
  
  const listName = await question('Enter list name (or press Enter for "Test Data"): ') || 'Test Data';
  
  console.log('\nChoose data type:');
  console.log('1. Single customer record');
  console.log('2. Multiple order records');
  console.log('3. Custom JSON data');
  
  const choice = await question('Choice (1-3): ');
  
  let data;
  switch (choice) {
    case '1':
      data = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        signup_date: new Date().toISOString(),
        status: 'active'
      };
      break;
    case '2':
      data = [
        {
          order_id: `ORD-${Date.now()}`,
          customer: 'Alice Johnson',
          product: 'Laptop',
          amount: 999.99,
          date: new Date().toISOString()
        },
        {
          order_id: `ORD-${Date.now() + 1}`,
          customer: 'Bob Smith',
          product: 'Mouse',
          amount: 29.99,
          date: new Date().toISOString()
        }
      ];
      break;
    case '3':
      const customJson = await question('Enter JSON data: ');
      try {
        data = JSON.parse(customJson);
      } catch (error) {
        console.log('❌ Invalid JSON, using default data');
        data = { message: 'Custom test data', timestamp: new Date().toISOString() };
      }
      break;
    default:
      data = { test: true, timestamp: new Date().toISOString() };
  }

  try {
    const response = await axios.post(`${BASE_URL}/webhook/receive`, {
      listName,
      data
    });
    console.log('\n✅ Webhook sent successfully!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('\n❌ Error sending webhook:', error.message);
  }
  
  await question('\nPress Enter to continue...');
}

async function viewLists() {
  console.log('\n📋 All Data Lists');
  console.log('=================');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/lists`);
    const lists = response.data;
    
    if (lists.length === 0) {
      console.log('No lists found. Send some webhook data first!');
    } else {
      lists.forEach((list, index) => {
        console.log(`\n${index + 1}. ${list.name} (ID: ${list.id})`);
        console.log(`   Description: ${list.description || 'No description'}`);
        console.log(`   Records: ${list.recordCount}`);
        console.log(`   Created: ${new Date(list.created_at).toLocaleString()}`);
      });
    }
  } catch (error) {
    console.log('❌ Error fetching lists:', error.message);
  }
  
  await question('\nPress Enter to continue...');
}

async function viewRecords() {
  console.log('\n👀 View Records from List');
  console.log('=========================');
  
  try {
    const listsResponse = await axios.get(`${BASE_URL}/api/lists`);
    const lists = listsResponse.data;
    
    if (lists.length === 0) {
      console.log('No lists found. Send some webhook data first!');
      await question('\nPress Enter to continue...');
      return;
    }
    
    console.log('\nAvailable lists:');
    lists.forEach((list, index) => {
      console.log(`${index + 1}. ${list.name} (${list.recordCount} records)`);
    });
    
    const choice = await question('\nEnter list number: ');
    const listIndex = parseInt(choice) - 1;
    
    if (listIndex >= 0 && listIndex < lists.length) {
      const listId = lists[listIndex].id;
      const recordsResponse = await axios.get(`${BASE_URL}/api/lists/${listId}/records`);
      const { records, pagination } = recordsResponse.data;
      
      console.log(`\n📊 Records from "${lists[listIndex].name}":`);
      console.log(`Showing ${records.length} of ${pagination.total} records\n`);
      
      records.forEach((record, index) => {
        console.log(`Record ${index + 1} (ID: ${record.id}):`);
        console.log(JSON.stringify(record.data, null, 2));
        console.log(`Created: ${new Date(record.created_at).toLocaleString()}`);
        console.log('---');
      });
    } else {
      console.log('❌ Invalid list number');
    }
  } catch (error) {
    console.log('❌ Error fetching records:', error.message);
  }
  
  await question('\nPress Enter to continue...');
}

async function sendWebhook() {
  console.log('\n🚀 Send Webhook to External Endpoint');
  console.log('====================================');
  
  try {
    const listsResponse = await axios.get(`${BASE_URL}/api/lists`);
    const lists = listsResponse.data;
    
    if (lists.length === 0) {
      console.log('No lists found. Send some webhook data first!');
      await question('\nPress Enter to continue...');
      return;
    }
    
    console.log('\nAvailable lists:');
    lists.forEach((list, index) => {
      console.log(`${index + 1}. ${list.name} (${list.recordCount} records)`);
    });
    
    const choice = await question('\nEnter list number: ');
    const listIndex = parseInt(choice) - 1;
    
    if (listIndex >= 0 && listIndex < lists.length) {
      const listId = lists[listIndex].id;
      
      console.log('\nTest endpoints:');
      console.log('1. https://httpbin.org/post (shows what was sent)');
      console.log('2. https://webhook.site (create your own endpoint)');
      console.log('3. Custom URL');
      
      const endpointChoice = await question('Choose endpoint (1-3): ');
      let endpointUrl;
      
      switch (endpointChoice) {
        case '1':
          endpointUrl = 'https://httpbin.org/post';
          break;
        case '2':
          console.log('Go to https://webhook.site to create a test endpoint');
          endpointUrl = await question('Enter your webhook.site URL: ');
          break;
        case '3':
          endpointUrl = await question('Enter custom URL: ');
          break;
        default:
          endpointUrl = 'https://httpbin.org/post';
      }
      
      const customData = {
        test: true,
        source: 'interactive-test',
        timestamp: new Date().toISOString()
      };
      
      console.log('\n🚀 Sending webhook...');
      const response = await axios.post(`${BASE_URL}/api/lists/${listId}/send-webhook`, {
        endpointUrl,
        customData
      });
      
      console.log('\n✅ Webhook sent!');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } else {
      console.log('❌ Invalid list number');
    }
  } catch (error) {
    console.log('❌ Error sending webhook:', error.message);
  }
  
  await question('\nPress Enter to continue...');
}

async function showExamples() {
  console.log('\n📖 Webhook Examples');
  console.log('===================');
  
  console.log('\n1. cURL Example:');
  console.log('curl -X POST http://localhost:3001/webhook/receive \\');
  console.log('  -H "Content-Type: application/json" \\');
  console.log('  -d \'{"listName": "My Data", "data": {"name": "John", "email": "<EMAIL>"}}\'');
  
  console.log('\n2. JavaScript/Node.js Example:');
  console.log(`const axios = require('axios');
  
axios.post('http://localhost:3001/webhook/receive', {
  listName: 'Customer Data',
  data: {
    name: 'Alice Johnson',
    email: '<EMAIL>',
    signup_date: new Date().toISOString()
  }
});`);
  
  console.log('\n3. Python Example:');
  console.log(`import requests
import json
from datetime import datetime

data = {
    "listName": "Sales Data",
    "data": {
        "order_id": "ORD-001",
        "customer": "Bob Smith",
        "amount": 99.99,
        "date": datetime.now().isoformat()
    }
}

response = requests.post(
    'http://localhost:3001/webhook/receive',
    headers={'Content-Type': 'application/json'},
    data=json.dumps(data)
)`);
  
  await question('\nPress Enter to continue...');
}

async function runFullTest() {
  console.log('\n🔄 Running Full Test Suite');
  console.log('==========================');
  
  try {
    // Import and run the test function
    const testFunction = require('./test-webhook.js');
    await testFunction();
  } catch (error) {
    console.log('❌ Error running tests:', error.message);
  }
  
  await question('\nPress Enter to continue...');
}

async function main() {
  console.log('🚀 Starting Interactive Testing Tool...\n');
  
  // Check if server is running
  try {
    await axios.get(`${BASE_URL}/api/lists`);
    console.log('✅ Server is running at http://localhost:3001\n');
  } catch (error) {
    console.log('❌ Server is not running! Please start it with: node server.js\n');
    rl.close();
    return;
  }
  
  while (true) {
    await showMenu();
    const choice = await question('Enter your choice (0-9): ');
    
    switch (choice) {
      case '1':
        await sendTestWebhook();
        break;
      case '2':
        await viewLists();
        break;
      case '3':
        await viewRecords();
        break;
      case '4':
        await sendWebhook();
        break;
      case '5':
        console.log('\n🏷️  Custom fields can be added via the web interface at http://localhost:3001');
        await question('\nPress Enter to continue...');
        break;
      case '6':
        console.log('\n📊 Webhook logs can be viewed via the web interface at http://localhost:3001');
        await question('\nPress Enter to continue...');
        break;
      case '7':
        console.log('\n🌐 Opening web interface...');
        console.log('Visit: http://localhost:3001');
        await question('\nPress Enter to continue...');
        break;
      case '8':
        await showExamples();
        break;
      case '9':
        await runFullTest();
        break;
      case '0':
        console.log('\n👋 Goodbye!');
        rl.close();
        return;
      default:
        console.log('\n❌ Invalid choice. Please try again.');
        await question('Press Enter to continue...');
    }
  }
}

main().catch(console.error);
