{"name": "manual-scraper-webhook-app", "version": "1.0.0", "description": "Webhook receiver app for spreadsheet data with UI management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm run dev", "build": "cd client && npm run build", "dev:full": "concurrently \"npm run dev\" \"npm run client\""}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "body-parser": "^1.20.2", "axios": "^1.6.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2"}, "keywords": ["webhook", "spreadsheet", "database", "api"], "author": "", "license": "MIT"}