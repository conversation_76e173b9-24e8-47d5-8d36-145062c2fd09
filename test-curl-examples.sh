#!/bin/bash

echo "🧪 Webhook Testing Examples with cURL"
echo "======================================"

BASE_URL="http://localhost:3001"

echo ""
echo "1. 📥 RECEIVING WEBHOOKS"
echo "------------------------"

echo ""
echo "Example 1: Single record"
curl -X POST $BASE_URL/webhook/receive \
  -H "Content-Type: application/json" \
  -d '{
    "listName": "Customer Data",
    "data": {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "signup_date": "2023-12-01T10:00:00Z"
    }
  }'

echo ""
echo ""
echo "Example 2: Multiple records (array)"
curl -X POST $BASE_URL/webhook/receive \
  -H "Content-Type: application/json" \
  -d '{
    "listName": "Sales Data",
    "data": [
      {
        "order_id": "ORD-001",
        "customer": "<PERSON>",
        "product": "Laptop",
        "amount": 999.99,
        "date": "2023-12-01T14:30:00Z"
      },
      {
        "order_id": "ORD-002", 
        "customer": "Carol Davis",
        "product": "Mouse",
        "amount": 29.99,
        "date": "2023-12-01T15:45:00Z"
      }
    ]
  }'

echo ""
echo ""
echo "Example 3: Using existing list ID"
curl -X POST $BASE_URL/webhook/receive \
  -H "Content-Type: application/json" \
  -d '{
    "listId": 1,
    "data": {
      "name": "David Wilson",
      "email": "<EMAIL>",
      "status": "premium"
    }
  }'

echo ""
echo ""
echo "2. 📤 VIEWING DATA"
echo "------------------"

echo ""
echo "Get all lists:"
curl -s $BASE_URL/api/lists | jq '.'

echo ""
echo "Get records from first list:"
curl -s $BASE_URL/api/lists/1/records | jq '.records[0:2]'

echo ""
echo ""
echo "3. 🚀 SENDING WEBHOOKS"
echo "----------------------"

echo ""
echo "Send webhook to httpbin.org (test endpoint):"
curl -X POST $BASE_URL/api/lists/1/send-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "endpointUrl": "https://httpbin.org/post",
    "customData": {
      "source": "manual_test",
      "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
    }
  }'

echo ""
echo ""
echo "4. 📊 CUSTOM FIELDS"
echo "-------------------"

echo ""
echo "Add custom field:"
curl -X POST $BASE_URL/api/lists/1/fields \
  -H "Content-Type: application/json" \
  -d '{
    "fieldName": "processing_status",
    "fieldType": "text",
    "fieldValue": "completed"
  }'

echo ""
echo ""
echo "Get custom fields:"
curl -s $BASE_URL/api/lists/1/fields | jq '.'

echo ""
echo ""
echo "✅ All tests completed!"
echo "🌐 Open http://localhost:3001 in your browser to see the web interface"
