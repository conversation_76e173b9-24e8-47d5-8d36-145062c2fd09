const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const N8N_WEBHOOK_URL = 'https://completewebops.app.n8n.cloud/webhook-test/e4f979c2-9415-4c30-a76d-201ff91a899e';

async function testN8nWebhook() {
  console.log('🚀 Testing n8n Webhook Integration');
  console.log('==================================');
  console.log('n8n Endpoint:', N8N_WEBHOOK_URL);
  console.log('');

  try {
    // Step 1: First, let's add some test data to our webhook app
    console.log('1. 📥 Adding test data to webhook app...');
    const webhookData = {
      listName: 'n8n Test Data',
      data: [
        {
          id: 'test-001',
          name: '<PERSON>',
          email: '<EMAIL>',
          company: 'Acme Corp',
          phone: '******-0123',
          source: 'webhook_test',
          timestamp: new Date().toISOString(),
          amount: 299.99,
          status: 'active'
        },
        {
          id: 'test-002',
          name: '<PERSON> <PERSON>',
          email: '<EMAIL>',
          company: 'Tech Solutions',
          phone: '******-0456',
          source: 'webhook_test',
          timestamp: new Date().toISOString(),
          amount: 149.99,
          status: 'pending'
        }
      ]
    };

    const addDataResponse = await axios.post(`${BASE_URL}/webhook/receive`, webhookData);
    console.log('✅ Test data added:', addDataResponse.data);
    
    const listId = addDataResponse.data.listId;

    // Step 2: Get the list to verify data
    console.log('\n2. 📋 Verifying stored data...');
    const listResponse = await axios.get(`${BASE_URL}/api/lists/${listId}`);
    const recordsResponse = await axios.get(`${BASE_URL}/api/lists/${listId}/records`);
    
    console.log('✅ List created:', listResponse.data.name);
    console.log('✅ Records stored:', recordsResponse.data.records.length);

    // Step 3: Send webhook to n8n
    console.log('\n3. 🌐 Sending webhook to n8n...');
    
    const webhookPayload = {
      endpointUrl: N8N_WEBHOOK_URL,
      customData: {
        source: 'local_webhook_app',
        test_id: 'n8n_integration_test',
        timestamp: new Date().toISOString(),
        app_version: '1.0.0',
        environment: 'local_development',
        message: 'Test webhook from local development environment'
      }
    };

    console.log('Sending to:', N8N_WEBHOOK_URL);
    console.log('Custom data:', JSON.stringify(webhookPayload.customData, null, 2));

    const sendResponse = await axios.post(`${BASE_URL}/api/lists/${listId}/send-webhook`, webhookPayload);
    
    console.log('\n✅ Webhook sent successfully!');
    console.log('Response status:', sendResponse.data.status);
    console.log('Success:', sendResponse.data.success);
    console.log('Records sent:', sendResponse.data.recordsSent);
    
    if (sendResponse.data.response) {
      console.log('n8n Response:', JSON.stringify(sendResponse.data.response, null, 2));
    }

    // Step 4: Check webhook logs
    console.log('\n4. 📊 Checking webhook logs...');
    const logsResponse = await axios.get(`${BASE_URL}/api/lists/${listId}/webhook-logs`);
    const latestLog = logsResponse.data[0];
    
    if (latestLog) {
      console.log('✅ Webhook logged successfully');
      console.log('Log ID:', latestLog.id);
      console.log('Sent at:', latestLog.sent_at);
      console.log('Response status:', latestLog.response_status);
      console.log('Endpoint:', latestLog.endpoint_url);
    }

    // Step 5: Test direct webhook to n8n (bypass our app)
    console.log('\n5. 🎯 Testing direct webhook to n8n...');
    
    const directPayload = {
      test_type: 'direct_webhook',
      source: 'local_test_script',
      timestamp: new Date().toISOString(),
      data: {
        message: 'Direct test to n8n webhook',
        user: 'test_user',
        action: 'webhook_test'
      }
    };

    try {
      const directResponse = await axios.post(N8N_WEBHOOK_URL, directPayload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Local-Webhook-Test/1.0'
        },
        timeout: 10000
      });
      
      console.log('✅ Direct webhook successful!');
      console.log('Status:', directResponse.status);
      console.log('Response:', JSON.stringify(directResponse.data, null, 2));
      
    } catch (directError) {
      console.log('⚠️  Direct webhook response:', directError.response?.status || 'No response');
      if (directError.response?.data) {
        console.log('Response data:', JSON.stringify(directError.response.data, null, 2));
      }
    }

    console.log('\n🎉 n8n Integration Test Complete!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Local webhook app is running');
    console.log('- ✅ Test data was stored successfully');
    console.log('- ✅ Webhook was sent to n8n endpoint');
    console.log('- ✅ Webhook delivery was logged');
    console.log('- ✅ Direct n8n webhook test completed');
    
    console.log('\n🔗 Next Steps:');
    console.log('1. Check your n8n workflow to see if it received the webhook');
    console.log('2. Use the web interface at http://localhost:3001 to send more webhooks');
    console.log('3. Monitor webhook logs in the web interface');

  } catch (error) {
    console.error('\n❌ Error during n8n webhook test:', error.message);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure your webhook app is running: node server.js');
    }
    
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
      console.error('💡 Check your internet connection and n8n webhook URL');
    }
  }
}

// Run the test
if (require.main === module) {
  testN8nWebhook();
}

module.exports = testN8nWebhook;
