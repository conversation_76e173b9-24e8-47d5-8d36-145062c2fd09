const express = require('express');
const app = express();
const PORT = 3002;

app.use(express.json({ limit: '10mb' }));

// Store received webhooks for testing
const receivedWebhooks = [];

// Test webhook receiver endpoint
app.post('/test-webhook', (req, res) => {
  const webhook = {
    timestamp: new Date().toISOString(),
    headers: req.headers,
    body: req.body,
    id: receivedWebhooks.length + 1
  };
  
  receivedWebhooks.push(webhook);
  
  console.log('🎯 Webhook received!');
  console.log('Timestamp:', webhook.timestamp);
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  console.log('Body:', JSON.stringify(req.body, null, 2));
  console.log('---');
  
  // Respond with success
  res.json({
    success: true,
    message: 'Webhook received successfully',
    id: webhook.id,
    timestamp: webhook.timestamp
  });
});

// View all received webhooks
app.get('/received-webhooks', (req, res) => {
  res.json({
    total: receivedWebhooks.length,
    webhooks: receivedWebhooks
  });
});

// Clear all received webhooks
app.delete('/received-webhooks', (req, res) => {
  receivedWebhooks.length = 0;
  res.json({ message: 'All webhooks cleared' });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'Test webhook receiver is running',
    received_count: receivedWebhooks.length
  });
});

app.listen(PORT, () => {
  console.log('🎯 Test Webhook Receiver running on port', PORT);
  console.log('📥 Send webhooks to: http://localhost:' + PORT + '/test-webhook');
  console.log('👀 View received: http://localhost:' + PORT + '/received-webhooks');
  console.log('🧹 Clear received: DELETE http://localhost:' + PORT + '/received-webhooks');
  console.log('');
  console.log('💡 Test command:');
  console.log(`curl -X POST http://localhost:${PORT}/test-webhook -H "Content-Type: application/json" -d '{"test": "data"}'`);
});

module.exports = app;
